{"name": "transcriber-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "format": "prettier --write .", "lint": "next lint"}, "dependencies": {"@livekit/components-react": "^2.6.10", "@livekit/krisp-noise-filter": "^0.2.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.15.0", "livekit-client": "^2.7.5", "lucide-react": "^0.469.0", "next": "^15.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.5.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^22.0.0", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "^15.2.3", "livekit-server-sdk": "^2.9.3", "postcss": "^8", "prettier": "3.4.2", "tailwindcss": "^3.4.1", "typescript": "^5"}}