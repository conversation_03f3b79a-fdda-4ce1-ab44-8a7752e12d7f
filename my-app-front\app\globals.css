@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Base colors - Dark Theme (default) */
  --background: #1a1f24;
  --foreground: #e6edf3;

  /* Background colors */
  --bg-primary: #242a33;
  --bg-secondary: #2d333b;
  --bg-tertiary: #3a424e;

  /* Brand colors */
  --primary: #2188ff;
  --primary-hover: #1f6feb;
  --primary-focus: #0366d6;
  --primary-muted: rgba(33, 136, 255, 0.5);
  --primary-subtle: rgba(33, 136, 255, 0.1);

  --secondary: #6e40c9;
  --secondary-hover: #5a32a3;
  --secondary-focus: #4c2889;
  --secondary-muted: rgba(110, 64, 201, 0.5);
  --secondary-subtle: rgba(110, 64, 201, 0.1);

  /* Semantic colors */
  --success: #2ea043;
  --success-hover: #238636;
  --success-focus: #1a7f37;
  --success-muted: rgba(46, 160, 67, 0.5);
  --success-subtle: rgba(46, 160, 67, 0.1);

  --danger: #f85149;
  --danger-hover: #da3633;
  --danger-focus: #b62324;
  --danger-muted: rgba(248, 81, 73, 0.5);
  --danger-subtle: rgba(248, 81, 73, 0.1);

  --warning: #e3b341;
  --warning-hover: #d29922;
  --warning-focus: #bb8009;
  --warning-muted: rgba(227, 179, 65, 0.5);
  --warning-subtle: rgba(227, 179, 65, 0.1);

  /* UI colors - Dark Theme */
  --border-color: #3a424e;
  --border-muted: #2d333b;
  --border-subtle: rgba(110, 118, 129, 0.1);

  --card-bg: #242a33;
  --hover-bg: #2d333b;
  --active-bg: #3a424e;

  --input-bg: #1a1f24;
  --input-border: #3a424e;
  --input-focus-border: #1f6feb;

  --button-bg: #2d333b;
  --button-hover-bg: #3a424e;
  --button-active-bg: #1f6feb;
  --button-text: #e6edf3;

  /* Text colors - Dark Theme */
  --text-primary: #e6edf3;
  --text-secondary: #7d8590;
  --text-tertiary: #6e7681;
  --text-placeholder: rgba(110, 118, 129, 0.5);
  --text-disabled: #484f58;
  --text-inverse: #1a1f24;

  /* Code syntax highlighting colors */
  --code-keyword: #ff7b72;
  --code-string: #a5d6ff;
  --code-boolean: #d2a8ff;
  --code-number: #79c0ff;
  --code-comment: #8b949e;
  --code-property: #d2a8ff;
  --code-selector: #7ee787;
  --code-operator: #ff7b72;
  --code-function: #d2a8ff;
  --code-variable: #79c0ff;

  /* Code block and editor styling */
  --bg-code: #1e1e1e;
  --bg-code-header: #252526;
  --border-code: #3e3e42;
  --text-code-primary: #e6edf3;
  --primary-button: #007acc;



  /* Language-specific colors */
  --lang-js: #f7df1e;
  --lang-ts: #3178c6;
  --lang-py: #3776ab;
  --lang-html: #e34c26;
  --lang-css: #264de4;
  --lang-jsx: #61dafb;
  --lang-tsx: #3178c6;
  --lang-json: #8bc34a;

  /* Effects */
  --shadow-color: rgba(0, 0, 0, 0.08);
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 1px 3px rgba(0, 0, 0, 0.03);
  --shadow-lg: 0 2px 5px rgba(0, 0, 0, 0.04);
  --shadow-focus: 0 0 0 2px rgba(33, 136, 255, 0.15);

  /* Scrollbar */
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  scrollbar-width: thin;

  /* Animation */
  --transition-fast: 150ms;
  --transition-normal: 250ms;
  --transition-slow: 350ms;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
}

html, body {
  height: 100%;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}



/* Custom scrollbar for Webkit browsers */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: var(--bg-primary);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  border: 2px solid var(--bg-primary);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

::-webkit-scrollbar-corner {
  background: var(--bg-primary);
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom animations */
@keyframes gradient-x {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}



.animate-gradient-x {
  background-size: 200% 100%;
  animation: gradient-x 8s ease infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

.animate-pulse-slow {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes ping-slow {
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.2;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

.animate-ping-slow {
  animation: ping-slow 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Code block styling */
.code-block-container {
  margin: 1rem 0;
  border-radius: 6px;
  overflow: hidden;
  background-color: #1a1f24; /* Darker background to match the app's dark theme */
  border: 1px solid #2d333b;
  font-family: var(--font-geist-mono), 'Consolas', 'Monaco', 'Andale Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}



.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #242a33; /* Match app's dark theme */
  border-bottom: 1px solid #2d333b;
}



.language-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.language-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #2188ff; /* Default blue color */
  box-shadow: 0 0 8px rgba(33, 136, 255, 0.5);
}

.language-tag {
  color: #e6edf3;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.copy-button {
  background-color: #2188ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.copy-button:hover {
  background-color: #1f6feb;
  transform: translateY(-1px);
}

.copy-button:active {
  transform: translateY(0);
}

.code-content {
  padding: 16px;
  overflow-x: auto;
  background-color: #1a1f24;
  color: #e6edf3;
}

.code-container {
  display: flex;
}

.line-numbers {
  display: flex;
  flex-direction: column;
  padding-right: 16px;
  border-right: 1px solid #2d333b;
  color: #7d8590;
  user-select: none;
  text-align: right;
  min-width: 40px;
  background-color: #1a1f24;
}

.code-lines {
  flex: 1;
  overflow-x: auto;
}

.code-lines pre {
  margin: 0;
  padding: 0;
  background-color: transparent;
  color: #e6edf3;
}

.code-lines code {
  font-family: inherit;
}

/* Code editor styling */
.code-editor-container {
  margin: 1rem 0;
  border-radius: 6px;
  overflow: hidden;
  background-color: #1a1f24;
  border: 1px solid #2d333b;
  font-family: var(--font-geist-mono), 'Consolas', 'Monaco', 'Andale Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.code-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #242a33;
  border-bottom: 1px solid #2d333b;
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.run-button {
  background-color: #3c873a;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.run-button:hover {
  background-color: #4cae4c;
}

.code-editor-content {
  display: flex;
  height: 200px;
}

.code-textarea {
  flex: 1;
  background-color: #1a1f24;
  color: #e6edf3;
  border: none;
  padding: 8px 16px;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  resize: none;
  outline: none;
  width: 100%;
  height: 100%;
  tab-size: 2;
  scrollbar-width: thin;
  scrollbar-color: #2d333b transparent;
}

.code-textarea::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.code-textarea::-webkit-scrollbar-track {
  background: transparent;
}

.code-textarea::-webkit-scrollbar-thumb {
  background-color: #2d333b;
  border-radius: 4px;
}

.code-textarea::-webkit-scrollbar-thumb:hover {
  background-color: #3a424e;
}

.code-textarea::placeholder {
  color: #7d8590;
  opacity: 0.6;
}

/* Syntax highlighting - Dark theme */
.keyword {
  color: #ff7b72;
  font-weight: 500;
}

.string {
  color: #a5d6ff;
}

.boolean {
  color: #d2a8ff;
  font-weight: 500;
}

.number {
  color: #79c0ff;
}

.comment {
  color: #8b949e;
  font-style: italic;
}

.selector {
  color: #7ee787;
}

.property {
  color: #d2a8ff;
}

.value {
  color: #a5d6ff;
}

.bracket {
  color: #e6edf3;
}

/* Sidebar toggle button styling */
.sidebar-toggle-btn {
  position: relative;
  overflow: hidden;
  will-change: transform, box-shadow;
  backface-visibility: hidden;
  transform: translateZ(0);
  animation: gentle-pulse 3s ease-in-out infinite;
}

.sidebar-toggle-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 6px;
  background: linear-gradient(135deg,
    rgba(33, 136, 255, 0.08),
    rgba(110, 64, 201, 0.08)
  );
  opacity: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-toggle-btn:hover {
  animation: none;
  box-shadow: 0 0 16px rgba(33, 136, 255, 0.25);
}

.sidebar-toggle-btn:hover::before {
  background: linear-gradient(135deg,
    rgba(33, 136, 255, 0.15),
    rgba(110, 64, 201, 0.15)
  );
}

/* Gentle pulse animation for sidebar toggle */
@keyframes gentle-pulse {
  0%, 100% {
    box-shadow: 0 0 8px rgba(33, 136, 255, 0.12);
  }
  50% {
    box-shadow: 0 0 12px rgba(33, 136, 255, 0.2);
  }
}
