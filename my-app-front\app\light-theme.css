/* Light Theme Styles */
.light-theme {
  --shadow-color: rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 1px rgba(0, 0, 0, 0.01);
  --shadow-md: 0 1px 2px rgba(0, 0, 0, 0.015);
  --shadow-lg: 0 2px 4px rgba(0, 0, 0, 0.02);

  --bg-primary: #ffffff;
  --bg-secondary: #f6f8fa;
  --bg-tertiary: #eaeef2;

  --text-primary: #24292f;
  --text-secondary: #57606a;
  --text-tertiary: #6e7781;
  --text-disabled: #a1a1aa;

  --border-default: #d0d7de;
  --border-muted: #eaeef2;

  --button-bg: #f6f8fa;
  --button-hover-bg: #eaeef2;
  --button-text: #24292f;

  --primary-default: #0969da;
  --primary-hover: #0969da;

  --code-bg: #f6f8fa;
  --code-border: #d0d7de;
  --code-text: #24292f;
}

/* Apply light theme to specific elements */
.light-theme body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Main background in light theme */
.light-theme .bg-bg-primary {
  background-color: var(--bg-primary) !important;
}

.light-theme .bg-bg-secondary {
  background-color: var(--bg-secondary) !important;
}

.light-theme .bg-bg-tertiary {
  background-color: var(--bg-tertiary) !important;
}

.light-theme .text-text-primary {
  color: var(--text-primary) !important;
}

.light-theme .text-text-secondary {
  color: var(--text-secondary) !important;
}

.light-theme .text-text-tertiary {
  color: var(--text-tertiary) !important;
}

.light-theme .border-border-DEFAULT {
  border-color: var(--border-default) !important;
}

.light-theme .border-border-muted {
  border-color: var(--border-muted) !important;
}

/* Header styling */
.light-theme header {
  background-color: var(--bg-secondary) !important;
  border-bottom-color: var(--border-default) !important;
}

/* Modal styling */
.light-theme .modal-content {
  background-color: var(--bg-primary) !important;
}

.light-theme .modal-header {
  background-color: var(--bg-secondary) !important;
}

/* Code block styling */
.light-theme .code-block-container {
  background-color: #ffffff !important;
  border: 1px solid #d0d7de !important;
}

.light-theme .code-header {
  background-color: #f3f3f3 !important;
  border-bottom: 1px solid #d0d7de !important;
}

.light-theme .language-tag {
  color: #57606a !important;
}

.light-theme .code-content {
  background-color: #ffffff !important;
  color: #24292f !important;
}

.light-theme .line-numbers {
  background-color: #ffffff !important;
  border-right: 1px solid #d0d7de !important;
  color: #6e7781 !important;
}

.light-theme .copy-button {
  background-color: #0969da !important;
  color: white !important;
}

/* Syntax highlighting for light theme */
.light-theme .keyword {
  color: #0000ff !important;
  font-weight: 500 !important;
}

.light-theme .string {
  color: #a31515 !important;
}

.light-theme .boolean {
  color: #0000ff !important;
  font-weight: 500 !important;
}

.light-theme .number {
  color: #098658 !important;
}

.light-theme .comment {
  color: #008000 !important;
  font-style: italic !important;
}

.light-theme .selector {
  color: #800000 !important;
}

.light-theme .property {
  color: #795e26 !important;
}

.light-theme .value {
  color: #a31515 !important;
}

.light-theme .bracket {
  color: #000000 !important;
}

/* Button styling */
.light-theme button.bg-button-bg {
  background-color: var(--button-bg) !important;
  color: var(--button-text) !important;
  box-shadow: var(--shadow-sm) !important;
}

.light-theme button.hover\:bg-button-hover-bg:hover {
  background-color: var(--button-hover-bg) !important;
}

/* Primary button styling */
.light-theme button.bg-primary-DEFAULT,
.light-theme button[variant="primary"],
.light-theme .bg-gradient-to-r.from-primary-DEFAULT,
.light-theme .bg-gradient-to-br.from-primary-DEFAULT {
  background-color: var(--primary-default) !important;
  background-image: none !important;
  color: white !important;
  box-shadow: var(--shadow-sm) !important;
}

.light-theme button.hover\:bg-primary-hover:hover {
  background-color: var(--primary-hover) !important;
}

/* Fix for space button in light theme */
.light-theme kbd {
  background-color: #e9ecef !important;
  color: #24292e !important;
  border-color: #d0d7de !important;
  box-shadow: none !important;
}

/* Fix for microphone button in light theme */
.light-theme div[class*="bg-bg-tertiary rounded-lg"] {
  background-color: #f0f2f5 !important;
  border-color: #e1e4e8 !important;
  box-shadow: none !important;
}

.light-theme div[class*="bg-bg-tertiary rounded-lg"][class*="border-primary-DEFAULT"] {
  border-color: var(--primary-default) !important;
  background-color: #f0f2f5 !important;
  box-shadow: none !important;
}

/* New microphone button styling */
.light-theme button[class*="rounded-full"][class*="bg-bg-tertiary"] {
  background-color: #f1f5f9 !important;
  color: #64748b !important;
}

.light-theme button[class*="rounded-full"][class*="bg-primary-DEFAULT"] {
  background-color: #0969da !important;
  color: white !important;
}

.light-theme .animate-ping-slow {
  background-color: rgba(9, 105, 218, 0.1) !important;
}

/* Fix for microphone button in light theme */
.light-theme .bg-bg-tertiary {
  color: var(--text-primary) !important;
}

/* Fix for device selector in light theme */
.light-theme .text-accent-text {
  color: var(--text-secondary) !important;
}

/* Fix for buttons in light theme */
.light-theme .text-white {
  color: white !important;
}

/* Fix for disconnect button in light theme */
.light-theme button[class*="text-danger-DEFAULT"] {
  background-color: white !important;
  border-color: var(--border-default) !important;
  color: #d73a49 !important;
}

.light-theme button[class*="text-danger-DEFAULT"]:hover {
  background-color: #fafbfc !important;
  border-color: #d73a49 !important;
  color: #d73a49 !important;
}



/* Fix for ghost buttons in light theme */
.light-theme button[class*="text-text-tertiary"],
.light-theme button[class*="text-text-secondary"] {
  color: var(--text-secondary) !important;
}

.light-theme button[class*="text-text-tertiary"]:hover,
.light-theme button[class*="text-text-secondary"]:hover {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* Clear button styling */
.light-theme button[class*="hover:text-danger-DEFAULT"] {
  color: #64748b !important;
}

.light-theme button[class*="hover:text-danger-DEFAULT"]:hover {
  background-color: #fee2e2 !important;
  color: #dc2626 !important;
}

/* Fix for code editor buttons in light theme */
.light-theme button[class*="bg-primary-DEFAULT"],
.light-theme button[class*="bg-gradient-to-r from-primary-DEFAULT"] {
  background-color: var(--primary-default) !important;
  background-image: none !important;
  color: white !important;
}

.light-theme button[class*="bg-primary-DEFAULT"]:hover {
  background-color: var(--primary-hover) !important;
}

.light-theme button[class*="bg-danger-DEFAULT"] {
  background-color: #d73a49 !important;
  color: white !important;
}

.light-theme button[class*="bg-danger-DEFAULT"]:hover {
  background-color: #cb2431 !important;
}

/* Input styling */
.light-theme input {
  background-color: var(--bg-primary) !important;
  border-color: var(--border-default) !important;
  color: var(--text-primary) !important;
}

.light-theme input::placeholder {
  color: var(--text-tertiary) !important;
}

/* Chat input container styling */
.light-theme .relative.flex-1.rounded-lg.overflow-hidden {
  background-color: white !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

/* Bottom action bar styling */
.light-theme div[class*="border-t border-bg-tertiary"] {
  background-color: #f8fafc !important;
  border-top: 1px solid #e2e8f0 !important;
}

/* Conversation list styling */
.light-theme div[class*="rounded-md hover:bg-bg-tertiary"] {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-muted) !important;
}

.light-theme div[class*="rounded-md hover:bg-bg-tertiary"]:hover {
  background-color: var(--bg-tertiary) !important;
}

.light-theme div[class*="rounded-md hover:bg-bg-tertiary"][class*="border-border-DEFAULT"] {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--border-default) !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Message preview styling */
.light-theme div[class*="text-[10px] text-text-secondary bg-bg-primary p-1 rounded-md"] {
  background-color: var(--bg-primary) !important;
  border: 1px solid var(--border-muted) !important;
}

/* Message bubbles in light theme */
.light-theme div[class*="bg-bg-secondary rounded-lg"],
.light-theme div[class*="bg-gradient-to-br from-bg-secondary"] {
  background-color: var(--bg-secondary) !important;
  background-image: none !important;
  border-color: var(--border-default) !important;
}

/* Device selector dropdown styling */
.light-theme div[class*="absolute bg-bg-secondary"] {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-default) !important;
  box-shadow: var(--shadow-sm) !important;
  transform: none !important;
}

/* Fix for device selector text orientation */
.light-theme .relative.z-10 .absolute {
  transform: none !important;
}

/* Scrollbar styling */
.light-theme ::-webkit-scrollbar {
  width: 12px !important;
  height: 12px !important;
}

.light-theme ::-webkit-scrollbar-track {
  background: var(--bg-primary) !important;
  border-radius: 6px !important;
}

.light-theme ::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2) !important;
  border-radius: 6px !important;
  border: 2px solid var(--bg-primary) !important;
}

.light-theme ::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3) !important;
}

.light-theme ::-webkit-scrollbar-corner {
  background: var(--bg-primary) !important;
}

/* Fix for language selector in light theme */
.light-theme button[class*="rounded-full bg-bg-tertiary"],
.light-theme button[class*="rounded-full text-text-secondary"] {
  background-color: #f1f5f9 !important;
  color: #64748b !important;
  border: 1px solid #e2e8f0 !important;
}

/* Teaching mode toggle button styling for light theme */
.light-theme .teaching-mode-toggle {
  background-color: rgba(130, 80, 223, 0.1) !important;
  border-color: #8250df !important;
  color: #8250df !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.light-theme .teaching-mode-toggle:hover {
  background-color: rgba(130, 80, 223, 0.15) !important;
}

.light-theme .teaching-mode-icon {
  color: inherit !important;
}

/* Send button styling */
.light-theme button[class*="text-primary-DEFAULT"] {
  color: #0969da !important;
}

.light-theme button[class*="text-primary-DEFAULT"]:hover {
  background-color: rgba(9, 105, 218, 0.1) !important;
}

/* Disabled send button */
.light-theme button[class*="text-text-tertiary"] {
  color: var(--text-disabled) !important;
}

.light-theme button[class*="rounded-full bg-bg-tertiary"]:hover {
  background-color: #e2e8f0 !important;
}

.light-theme div[class*="absolute z-10 mt-1 w-full bg-bg-primary rounded-md"] {
  background-color: white !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}
