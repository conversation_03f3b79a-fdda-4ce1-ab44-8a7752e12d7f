"use client";

import { useState, useCallback, useEffect } from 'react';
import { useMaybeRoomContext } from '@livekit/components-react';
import { ConnectionState, Room } from 'livekit-client';
import { useAIResponses } from './use-ai-responses';
import { useTranscriber } from './use-transcriber';
import { useAuth } from './use-auth';

// Import utilities from conversation-utils to avoid duplication
import { isRoomConnected, publishDataWithRetry } from '@/utils/conversation-utils';

/**
 * Message type enumeration for conversation messages.
 * Defines whether a message was sent by the user or generated by AI.
 */
export type MessageType = 'user' | 'ai';

/**
 * Complete message interface representing a single conversation message.
 * Supports both simple messages and complex multi-part AI responses.
 *
 * @interface Message
 * @property {string} id - Unique identifier for the message
 * @property {MessageType} type - Whether message is from 'user' or 'ai'
 * @property {string} text - The actual message content/text
 * @property {number} timestamp - Unix timestamp when message was created
 * @property {string} [conversation_id] - Optional conversation ID for message grouping
 * @property {boolean} [isPart] - Whether this message is part of a multi-part response
 * @property {number} [partNumber] - Part number in multi-part message sequence
 * @property {number} [totalParts] - Total number of parts in multi-part message
 * @property {boolean} [isFinal] - Whether this is the final part of a multi-part message
 * @property {boolean} [error] - Whether this message represents an error state
 */
export interface Message {
  id: string;
  type: MessageType;
  text: string;
  timestamp: number;
  conversation_id?: string; // Optional to maintain compatibility with existing code
  // Properties for multi-part messages
  isPart?: boolean;
  partNumber?: number;
  totalParts?: number;
  isFinal?: boolean;
  // Property for error handling
  error?: boolean;
}

/**
 * Custom hook for managing conversation state and real-time messaging.
 * Handles message storage, WebRTC communication, and conversation lifecycle.
 *
 * Features:
 * - Real-time message synchronization with backend via WebRTC
 * - Multi-part AI response handling
 * - Conversation switching and persistence
 * - Teaching mode integration
 * - Error handling and retry logic
 * - User authentication integration
 *
 * @returns {Object} Conversation state and management functions
 */
export function useConversation() {
  // Get the current authenticated user for message attribution
  const { user } = useAuth();

  /**
   * Effect hook that runs on component mount to initialize conversation state.
   * Clears legacy localStorage data and sets up event listeners for user logout.
   */
  useEffect(() => {
    // Only run in browser environment
    if (typeof window !== 'undefined') {
      // Remove legacy localStorage messages since we now rely on backend storage
      localStorage.removeItem('conversation-messages');

      /**
       * Event handler for user logout events.
       * Clears all conversation state when user logs out to prevent data leakage.
       */
      const handleUserLogout = () => {
        setMessages([]); // Clear all messages from state
        setCurrentConversationId(null); // Clear current conversation ID
      };

      // Listen for global logout events dispatched by auth system
      window.addEventListener('user-logged-out', handleUserLogout);

      // Cleanup event listener to prevent memory leaks
      return () => {
        window.removeEventListener('user-logged-out', handleUserLogout);
      };
    }
  }, []); // Empty dependency array - run only once on mount

  // State to track the current active conversation ID
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

  /**
   * Effect hook to monitor conversation ID changes and handle conversation switching.
   * Listens for localStorage changes, storage events, and mode switch events.
   * Automatically clears messages when switching between conversations.
   */
  useEffect(() => {
    // Initial check for existing conversation ID in localStorage
    const newId = localStorage.getItem('current-conversation-id');
    if (newId !== currentConversationId) {
      setMessages([]); // Clear messages when switching conversations
      setCurrentConversationId(newId); // Update current conversation ID
    }

    /**
     * Event handler for localStorage storage events.
     * Responds to conversation ID changes from other tabs or components.
     *
     * @param {StorageEvent} e - Storage event containing key and new value
     */
    const handleStorageChange = (e: StorageEvent) => {
      // Only handle changes to the conversation ID key
      if (e.key === 'current-conversation-id') {
        const newId = e.newValue; // Get new conversation ID from event
        if (newId !== currentConversationId) {
          setMessages([]); // Clear messages when switching conversations
          setCurrentConversationId(newId); // Update current conversation ID
        }
      }
    };

    /**
     * Fallback function to check for conversation changes via polling.
     * Used when storage events don't fire reliably (same-tab changes).
     */
    const checkForConversationChanges = () => {
      const newId = localStorage.getItem('current-conversation-id');
      if (newId !== currentConversationId) {
        setMessages([]); // Clear messages when switching conversations
        setCurrentConversationId(newId); // Update current conversation ID
      }
    };

    /**
     * Event handler for teaching mode switch events.
     * Clears all conversation state and forces creation of new conversation.
     */
    const handleModeSwitch = () => {
      setMessages([]); // Clear all messages
      setLastProcessedTranscription(''); // Reset transcription tracking
      setLastProcessedResponseId(''); // Reset response tracking

      // Clear the current conversation ID to force creating a new one
      setCurrentConversationId(null);
      localStorage.removeItem('current-conversation-id');
    };

    // Add event listener for storage changes from other tabs
    window.addEventListener('storage', handleStorageChange);

    // Check for changes every 300ms as a fallback for same-tab changes
    const interval = setInterval(checkForConversationChanges, 300);

    // Listen for mode switch events from settings changes
    window.addEventListener('create-new-conversation-for-mode-switch', handleModeSwitch);

    // Cleanup all event listeners and intervals
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('create-new-conversation-for-mode-switch', handleModeSwitch);
      clearInterval(interval);
    };
  }, [currentConversationId]); // Re-run when currentConversationId changes

  const [messages, setMessages] = useState<Message[]>([]);
  const room = useMaybeRoomContext();
  const { responses } = useAIResponses();
  const { transcriptions } = useTranscriber();



  // Track the last processed transcription and response
  const [lastProcessedTranscription, setLastProcessedTranscription] = useState<string>('');
  const [lastProcessedResponseId, setLastProcessedResponseId] = useState<string>('');

  // Helper function to get the current conversation's teaching mode
  const getCurrentConversationMode = useCallback((): string | null => {
    // Get the current conversation ID from localStorage
    const storedConversationId = localStorage.getItem('current-conversation-id');

    // Try to get the mode from the current conversation
    if (storedConversationId) {
      try {
        // First try to get it from the state if available
        if (currentConversationId === storedConversationId) {
          // Check localStorage for conversation data
          const conversationData = localStorage.getItem(`conversation-${storedConversationId}`);
          if (conversationData) {
            try {
              const parsedData = JSON.parse(conversationData);
              if (parsedData && parsedData.teaching_mode) {
                return parsedData.teaching_mode;
              }
            } catch (parseError) {
              // Silently handle parsing errors
            }
          }
        }

        // If we can't get it from state, try to infer it from settings
        // This is a fallback and might not be accurate
        const storedSettings = localStorage.getItem("app-settings");
        if (storedSettings) {
          const parsedSettings = JSON.parse(storedSettings);
          if (parsedSettings && parsedSettings.teachingMode) {
            return parsedSettings.teachingMode;
          }
        }
      } catch (e) {
        // Silently handle errors getting conversation mode
      }
    }

    // Default to teacher mode if we can't determine
    return 'teacher';
  }, [currentConversationId]);

  // Listen for user message echoes from the backend
  useEffect(() => {
    if (!room) return;

    const handleDataReceived = (payload: any, participant?: any, kind?: any, topic?: any) => {
      // Parameters are provided by the event but not all are used in this handler
      void participant;
      void kind;
      try {
        // Handle binary audio data with specific topic
        if (topic === "binary_audio") {
          // Let the use-ai-responses hook handle the audio playback
          return;
        }

        // Handle audio info with specific topic
        if (topic === "audio_info") {
          // Just acknowledge receipt - no processing needed
          return;
        }

        // Try to decode as JSON for other messages
        const dataString = new TextDecoder().decode(payload);
        let data;

        try {
          data = JSON.parse(dataString);

          // Handle audio-related messages - just acknowledge receipt
          if (data.type === "audio_data_info" ||
              data.type === "audio_data" ||
              data.type === "tts_audio_url") {
            return;
          }
        } catch (e) {
          // If it's not valid JSON, it might be binary audio data
          // Let the use-ai-responses hook handle it
          return;
        }

        if (data.type === "conversation_data") {
          // Clear existing messages and load the conversation messages
          const conversationMessages: Message[] = data.conversation.messages.map((msg: any) => ({
            id: msg.id,
            type: msg.type as MessageType,
            text: msg.content,
            timestamp: new Date(msg.timestamp).getTime(),
            conversation_id: data.conversation.id // Add conversation_id to track which conversation this message belongs to
          }));

          // Replace all messages with the ones from the selected conversation
          setMessages(conversationMessages);

          // Directly update the current conversation ID in state
          setCurrentConversationId(data.conversation.id);

          // Reset tracking variables
          setLastProcessedTranscription('');
          setLastProcessedResponseId('');

          // Store the current conversation ID
          localStorage.setItem('current-conversation-id', data.conversation.id);
        } else if (data.type === "user_message_echo") {
          // Get the current conversation ID
          const currentConversationId = localStorage.getItem('current-conversation-id') || data.conversation_id;

          // Add the echoed message to the conversation
          const newMessage: Message = {
            id: `user-echo-${Date.now()}`,
            type: 'user',
            text: data.text,
            timestamp: Date.now(),
            conversation_id: currentConversationId
          };

          // Always add the message - we're clearing history on startup so no need to check for duplicates
          setMessages(prev => {
            const newMessages = [...prev, newMessage];
            // No need to store in localStorage as we're clearing it on startup
            return newMessages;
          });
        } else if (data.type === "ai_response") {
          // Get the current conversation ID
          const currentConversationId = localStorage.getItem('current-conversation-id') || data.conversation_id;

          // Check if this is part of a multi-part message
          // Use optional chaining to safely access properties
          const isPart = data.is_part === true;
          const partNumber = data.part_number || 1;
          const totalParts = data.total_parts || 1;
          const isFinal = data.is_final === true;

          // Create a unique ID for this message
          // For multi-part messages, include the part number in the ID
          const messageId = isPart
            ? `ai-response-part-${partNumber}-of-${totalParts}-${Date.now()}`
            : `ai-response-${Date.now()}`;

          // Add the AI response to the conversation
          const newMessage: Message = {
            id: messageId,
            type: 'ai',
            text: data.text || '',
            timestamp: Date.now(),
            conversation_id: currentConversationId,
            // Add metadata for multi-part messages with default values
            isPart: isPart || false,
            partNumber: partNumber || 1,
            totalParts: totalParts || 1,
            isFinal: isFinal || true
          };

          // Add the AI response to the messages, but check for duplicates first
          setMessages(prev => {
            // Check if this is a duplicate message (same text content)
            const isDuplicate = prev.some(msg =>
              msg.type === 'ai' &&
              msg.text === data.text &&
              Date.now() - msg.timestamp < 5000 // Only check messages from the last 5 seconds
            );

            // If it's a duplicate, don't add it
            if (isDuplicate) {
              return prev;
            }

            // If the message is empty, add a placeholder
            if (!data.text || !data.text.trim()) {
              newMessage.text = ' '; // Add a space to prevent rendering issues
            }

            const newMessages = [...prev, newMessage];
            return newMessages;
          });
        }
      } catch (e) {
        // Silently handle data parsing errors
      }
    };

    room.on('dataReceived', handleDataReceived);
    return () => {
      room.off('dataReceived', handleDataReceived);
    };
  }, [room]);

  // Process transcriptions and add them to messages
  useEffect(() => {
    const transcriptionText = Object.values(transcriptions)
      .toSorted((a, b) => a.firstReceivedTime - b.firstReceivedTime)
      .map((t) => t.text.trim())
      .join("\n");

    if (transcriptionText && transcriptionText !== lastProcessedTranscription) {
      setLastProcessedTranscription(transcriptionText);

      // We'll let the server handle adding the message to the conversation
      // The message will be added when we receive the echo from the server
      // Processed transcription, waiting for server echo
    }
  }, [transcriptions, lastProcessedTranscription]);

  // Process AI responses and add them to messages
  useEffect(() => {
    // We'll let the server handle adding AI responses to the conversation
    // The message will be added when we receive the AI response from the server
    if (responses.length > 0) {
      const lastResponse = responses[responses.length - 1];

      if (lastResponse.id !== lastProcessedResponseId) {
        setLastProcessedResponseId(lastResponse.id);
        // Processed AI response, waiting for server message
      }
    }
  }, [responses, lastProcessedResponseId]);

  // Add a user message to the conversation
  const addUserMessage = useCallback(async (text: string) => {
    if (!text.trim()) return;

    // Sending text input to backend
    if (room) {
      try {
        // Get the current teaching mode from settings
        let teachingMode = 'teacher'; // Default to teacher mode
        try {
          const storedSettings = localStorage.getItem("app-settings");
          if (storedSettings) {
            const parsedSettings = JSON.parse(storedSettings);
            teachingMode = parsedSettings.teachingMode || 'teacher';
          }
        } catch (e) {
          // Silently handle settings parsing errors
        }

        // Get the current conversation ID from localStorage
        const storedConversationId = localStorage.getItem('current-conversation-id');

        // Check if we need to create a new conversation for this message
        // This happens when there's no conversation or the current conversation mode doesn't match the current teaching mode
        const currentMode = getCurrentConversationMode();

        // Always force a new conversation if we don't have a valid stored ID
        // This ensures we don't try to send messages to non-existent conversations
        const needsNewConversation = !storedConversationId ||
          storedConversationId === 'null' ||
          storedConversationId === 'undefined' ||
          (storedConversationId && teachingMode !== currentMode);

        const message = {
          type: "text_input",
          text: text.trim(),
          teaching_mode: teachingMode,
          new_conversation: needsNewConversation,
          user_id: user?.id // Include user ID for data isolation
        };

        // Check if the room is connected before attempting to publish
        if (room.state !== ConnectionState.Connected) {
          // Room not connected, attempting to reconnect

          // Wait for the room to reconnect (up to 5 seconds)
          for (let i = 0; i < 5; i++) {
            await new Promise(resolve => setTimeout(resolve, 1000));
            if (isRoomConnected(room)) {
              break;
            }

            // If we've waited 5 seconds and still not connected, throw an error
            if (i === 4) {
              throw new Error('Room failed to reconnect after 5 seconds');
            }
          }
        }

        // We'll wait for the echo from the server to add the message to the conversation
        // Use utility function for robust publishing with retry logic
        await publishDataWithRetry(room, message);

        // Get the current conversation ID
        const currentConversationId = localStorage.getItem('current-conversation-id');

        // For better UX, we can still return a message object, but it won't be added to the state yet
        return {
          id: `user-pending-${Date.now()}`,
          type: 'user' as MessageType,
          text: text.trim(),
          timestamp: Date.now(),
          conversation_id: currentConversationId || undefined
        };
      } catch (error) {
        // Error sending text input - message will appear with error indicator
        const currentConversationId = localStorage.getItem('current-conversation-id');
        return {
          id: `user-error-${Date.now()}`,
          type: 'user' as MessageType,
          text: text.trim(),
          timestamp: Date.now(),
          conversation_id: currentConversationId || undefined,
          error: true
        };
      }
    }

    return undefined;
  }, [room, currentConversationId, getCurrentConversationMode, user]);

  // Clear all messages and create a new conversation
  const clearMessages = useCallback(() => {
    setMessages([]);
    setLastProcessedTranscription('');
    setLastProcessedResponseId('');

    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('conversation-messages');
      localStorage.removeItem('current-conversation-id');
    }

    // Reset the current conversation ID
    setCurrentConversationId(null);

    // Create a new conversation
    if (room) {
      // Get the current teaching mode from settings
      let teachingMode = 'teacher'; // Default to teacher mode
      try {
        const storedSettings = localStorage.getItem("app-settings");
        if (storedSettings) {
          const parsedSettings = JSON.parse(storedSettings);
          teachingMode = parsedSettings.teachingMode || 'teacher';
        }
      } catch (e) {
        // Silently handle settings parsing errors
      }

      const message = {
        type: "new_conversation",
        title: `New Conversation`,
        teaching_mode: teachingMode
      };

      // Use a safe approach with connection checking and retry logic
      const createNewConversation = async () => {
        try {
          // Check if the room is connected before attempting to publish
          if (room.state !== ConnectionState.Connected) {
            // Room not connected, waiting before creating new conversation
            // Wait for the room to connect before trying to create a new conversation
            setTimeout(createNewConversation, 1000);
            return;
          }

          // Use utility function for robust publishing with retry logic
          await publishDataWithRetry(room, message);
        } catch (error) {
          // Silently handle conversation creation errors
        }
      };

      // Start the process
      createNewConversation();
    }
  }, [room]);

  // Function to send a hidden instruction to the AI without showing it in the chat
  const sendHiddenInstruction = useCallback((text: string) => {
    if (!room) return;

    try {
      // Get the current teaching mode from settings
      let teachingMode = 'teacher'; // Default to teacher mode
      try {
        const storedSettings = localStorage.getItem("app-settings");
        if (storedSettings) {
          const parsedSettings = JSON.parse(storedSettings);
          teachingMode = parsedSettings.teachingMode || 'teacher';
        }
      } catch (e) {
        // Silently handle settings parsing errors
      }

      // Get the current conversation ID from localStorage
      const storedConversationId = localStorage.getItem('current-conversation-id');

      // Check if we need to create a new conversation for this message
      // This happens when there's no conversation or the current conversation mode doesn't match the current teaching mode
      const currentMode = getCurrentConversationMode();

      // Always force a new conversation if we don't have a valid stored ID
      // This ensures we don't try to send messages to non-existent conversations
      const needsNewConversation = !storedConversationId ||
        storedConversationId === 'null' ||
        storedConversationId === 'undefined' ||
        (storedConversationId && teachingMode !== currentMode);

      // Create a message with a special flag indicating it's a hidden instruction
      const message = {
        type: "text_input",
        text: text.trim(),
        teaching_mode: teachingMode,
        hidden: true, // This flag tells the backend not to echo the message back
        new_conversation: needsNewConversation,
        user_id: user?.id // Include user ID for data isolation
      };

      // Send the message to the backend
      if (room.state === ConnectionState.Connected) {
        room.localParticipant.publishData(
          new TextEncoder().encode(JSON.stringify(message))
        );
      }
    } catch (error) {
      // Silently handle hidden instruction errors
    }
  }, [room, currentConversationId, getCurrentConversationMode, user]);

  return {
    messages,
    addUserMessage,
    sendTextMessage: addUserMessage, // Export addUserMessage as sendTextMessage for backward compatibility
    sendHiddenInstruction, // Export the new function
    clearMessages,
    currentConversationId
  };
}
