import { useState, useCallback, useEffect } from 'react';
import { useSettings } from './use-settings';
import {
  cleanTextForTTS,
  removeSpecialSectionMarkers,
  addNaturalPauses,
  extractExplanationBlocks,
  hasSpecialSections
} from '@/utils/text-cleaning';

// Helper function to find the best voice
const getBestVoice = (availableVoices: SpeechSynthesisVoice[]): SpeechSynthesisVoice | null => {
  // Safety check
  if (!availableVoices || availableVoices.length === 0) {
    return null;
  }

  try {
    // Define preferred high-quality female voices in order
    const preferredVoices = [
      // Microsoft high-quality voices
      'Microsoft Aria Online (Natural)', // Windows 11 high-quality
      'Microsoft Jessa Online (Natural)', // Windows 11 high-quality
      'Microsoft Zira Online (Natural)', // Windows 11 high-quality
      'Microsoft Jenny Online (Natural)', // Windows 11 high-quality
      'Microsoft Sarah Online (Natural)', // Windows 11 high-quality

      // Google high-quality voices
      'Google UK English Female', // Chrome
      'Google US English Female', // Chrome
      'Google US English', // Chrome (often female)

      // Apple high-quality voices
      'Samantha',                // macOS/iOS (high quality)
      'Ava (Premium)',           // macOS/iOS
      'Allison',                 // macOS/iOS
      'Victoria',                // macOS
      'Karen',                   // macOS
      'Moira',                   // macOS
      'Tessa',                   // macOS
      'Fiona',                   // macOS

      // Microsoft standard voices
      'Microsoft Zira Desktop',  // Windows
      'Microsoft Hazel Desktop', // Windows
      'Microsoft Heera Desktop', // Windows

      // Other languages as fallback
      'Google español femenino', // Chrome
      'Google français femenin', // Chrome
    ];

    // Try to find one of our preferred voices
    for (const preferredVoice of preferredVoices) {
      const voice = availableVoices.find(v => v && v.name === preferredVoice);
      if (voice) {
        return voice;
      }
    }

    // If no preferred voice is found, try to find any female voice
    // Female voices often have 'female', 'woman', or common female names in their name
    const femaleVoiceIndicators = [
      'female', 'woman', 'girl', 'feminine', 'femenin', 'femenino',
      // Common female names
      'samantha', 'karen', 'zira', 'victoria', 'moira', 'jenny', 'sarah',
      'aria', 'jessa', 'allison', 'ava', 'hazel', 'heera', 'tessa', 'fiona'
    ];

    // First try to find a high-quality voice
    for (const voice of availableVoices) {
      if (!voice || !voice.name) continue;

      const lowerName = voice.name.toLowerCase();
      // Look for indicators of high-quality voices
      if ((lowerName.includes('natural') || lowerName.includes('premium') || lowerName.includes('online')) &&
          femaleVoiceIndicators.some(indicator => lowerName.includes(indicator))) {
        return voice;
      }
    }

    // Then try to find any female voice
    for (const voice of availableVoices) {
      if (!voice || !voice.name) continue;

      const lowerName = voice.name.toLowerCase();
      if (femaleVoiceIndicators.some(indicator => lowerName.includes(indicator))) {
        return voice;
      }
    }

    // If no female voice is found, return the first valid voice
    for (const voice of availableVoices) {
      if (voice && voice.name) {
        return voice;
      }
    }

    // Last resort
    return availableVoices[0];
  } catch (error) {
    return null;
  }
};

export function useWebTTS() {
  const { settings } = useSettings();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<SpeechSynthesisVoice | null>(null);

  // Initialize speech synthesis and load voices
  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    if (!window.speechSynthesis) {
      return;
    }



    // Load available voices
    const loadVoices = () => {
      try {
        const availableVoices = window.speechSynthesis.getVoices();

        if (availableVoices && availableVoices.length > 0) {
          setVoices(availableVoices);

          // Use the getBestVoice function to select optimal voice
          const bestVoice = getBestVoice(availableVoices);
          if (bestVoice) {
            setSelectedVoice(bestVoice);
          }
        }
      } catch (error) {
        console.error('Error loading voices:', error);
      }
    };

    // Try to set up voice change listener
    try {
      // Chrome loads voices asynchronously
      if (window.speechSynthesis.onvoiceschanged !== undefined) {
        window.speechSynthesis.onvoiceschanged = loadVoices;
      }

      // Try to load voices immediately (works in Firefox/Safari)
      loadVoices();
    } catch (error) {
      console.error('Error setting up voice listener:', error);
    }

    // Fix for Chrome issue where speech synthesis stops after ~15 seconds
    let intervalId: NodeJS.Timeout;
    try {
      intervalId = setInterval(() => {
        if (window.speechSynthesis && window.speechSynthesis.speaking) {
          try {
            window.speechSynthesis.pause();
            window.speechSynthesis.resume();
          } catch (e) {
            // Silently handle resume errors
          }
        }
      }, 10000);
    } catch (error) {
      console.error('Error setting up speech synthesis interval:', error);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [selectedVoice]);

  // Function to stop speaking
  const stopSpeaking = useCallback(() => {
    if (typeof window === 'undefined') return;

    try {
      if (window.speechSynthesis) {
        window.speechSynthesis.cancel();
      }
    } catch (error) {
      console.error('Error stopping speech synthesis:', error);
    }

    setIsPlaying(false);
  }, []);

  // Function to process text for course content
  const processCourseContent = useCallback((text: string): string => {
    // Split the text into lines to process each line appropriately
    const lines = text.split('\n');
    const processedLines: string[] = [];

    // Track if we're in a code block
    let inCodeBlock = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Skip empty lines
      if (!line) continue;

      // Check for code block markers
      if (line.startsWith('```')) {
        inCodeBlock = !inCodeBlock;
        // Skip code block markers without adding any text to be read
        continue;
      }

      // Skip content inside code blocks
      if (inCodeBlock) continue;

      // Process headings without adding prefixes, just the content
      if (line.startsWith('# ')) {
        // Course title - just add the title text
        processedLines.push(`${line.substring(2)}`);
        processedLines.push('');  // Add pause
      } else if (line.match(/^## Chapter \d+:/)) {
        // Chapter heading - just add the heading text
        processedLines.push(`${line.substring(3)}`);
        processedLines.push('');  // Add pause
      } else if (line.startsWith('### ')) {
        // Section heading - just add the heading text
        processedLines.push(`${line.substring(4)}`);
        processedLines.push('');  // Add pause
      } else if (line.startsWith('#### ')) {
        // Subsection heading - just add the heading text without prefix
        const heading = line.substring(5);
        processedLines.push(`${heading}`);
        processedLines.push('');  // Add pause
      } else if (line.match(/^\s*[\-\*]\s/)) {
        // Bullet point - just add the content without bullet marker
        processedLines.push(`${line.replace(/^\s*[\-\*]\s/, '')}`);
      } else if (line.match(/^\s*\d+\.\s/)) {
        // Numbered list - just add the content without number
        const content = line.replace(/^\s*\d+\.\s/, '');
        processedLines.push(`${content}`);
      } else if (line.startsWith('> ')) {
        // Blockquote - just add the content without the marker
        processedLines.push(`${line.substring(2)}`);
      } else {
        // Regular paragraph
        processedLines.push(line);
      }
    }

    // Join the processed lines with appropriate spacing
    return processedLines.join(' ');
  }, []);

  // Function to speak text
  const speak = useCallback((text: string, voiceOverride?: SpeechSynthesisVoice) => {
    if (typeof window === 'undefined') {
      return false;
    }

    if (!window.speechSynthesis) {
      return false;
    }

    if (!text || !text.trim()) {
      return false;
    }

    try {
      // Stop any ongoing speech
      stopSpeaking();

      setIsLoading(true);
      setError(null);

      // Check if this is course content
      const isCourseContent = text.includes('# Complete Course:') ||
                             text.includes('## Chapter') ||
                             text.includes('### ');

      // Check for special sections with [EXPLAIN] markers - more robust check
      const hasSpecialSectionsDetected = hasSpecialSections(text);

      if (hasSpecialSectionsDetected) {

        // Define regex patterns to detect incomplete markers
        const openExplainRegex = /\[\s*EXPLAIN\s*\](?![\s\S]*?\[\s*\/\s*EXPLAIN\s*\])/g;
        const closeExplainRegex = /\[\s*\/\s*EXPLAIN\s*\](?<!\[\s*EXPLAIN\s*\][\s\S]*?)/g;
        const openCodeRegex = /\[\s*CODE\s*\](?![\s\S]*?\[\s*\/\s*CODE\s*\])/g;
        const closeCodeRegex = /\[\s*\/\s*CODE\s*\](?<!\[\s*CODE\s*\][\s\S]*?)/g;

        // Check for incomplete markers
        const hasOpenExplain = openExplainRegex.test(text);
        const hasCloseExplain = closeExplainRegex.test(text);
        const hasOpenCode = openCodeRegex.test(text);
        const hasCloseCode = closeCodeRegex.test(text);

        // Reset regex lastIndex
        openExplainRegex.lastIndex = 0;
        closeExplainRegex.lastIndex = 0;
        openCodeRegex.lastIndex = 0;
        closeCodeRegex.lastIndex = 0;

        if (hasOpenExplain || hasCloseExplain || hasOpenCode || hasCloseCode) {

          // Fix incomplete markers for TTS
          let fixedText = text;

          // Fix open [EXPLAIN] without close by adding a closing tag at the end
          if (hasOpenExplain) {
            fixedText = fixedText.replace(openExplainRegex, (match) => {
              return match + "\n\nExplanation\n\n[/EXPLAIN]";
            });
          }

          // Fix close [/EXPLAIN] without open by adding an opening tag before it
          if (hasCloseExplain) {
            fixedText = fixedText.replace(closeExplainRegex, (match) => {
              return "[EXPLAIN]\n\nExplanation\n\n" + match;
            });
          }

          // Fix open [CODE] without close by adding a closing tag at the end
          if (hasOpenCode) {
            fixedText = fixedText.replace(openCodeRegex, (match) => {
              return match + "\n\n```\n\n```\n\n[/CODE]";
            });
          }

          // Fix close [/CODE] without open by adding an opening tag before it
          if (hasCloseCode) {
            fixedText = fixedText.replace(closeCodeRegex, (match) => {
              return "[CODE]\n\n```\n\n```\n\n" + match;
            });
          }

          // Use the fixed text
          text = fixedText;
        }
      }

      // Process text differently based on content type
      let cleanedText;

      // If we have special sections and skip explanations is enabled, remove explanations
      if (hasSpecialSectionsDetected && settings.ttsSkipExplanations) {
        try {
          // Remove explanation blocks from the text
          let textWithoutExplanations = text;

          // Remove [EXPLAIN]...[/EXPLAIN] blocks
          const explainBlockRegex = /\[\s*EXPLAIN\s*\][\s\S]*?\[\s*\/\s*EXPLAIN\s*\]/g;
          textWithoutExplanations = textWithoutExplanations.replace(explainBlockRegex, '');

          // Clean up any extra whitespace left behind
          textWithoutExplanations = textWithoutExplanations.replace(/\n\s*\n\s*\n/g, '\n\n');

          // Apply standard text cleaning
          cleanedText = removeSpecialSectionMarkers(textWithoutExplanations);
          cleanedText = cleanTextForTTS(cleanedText);
          cleanedText = addNaturalPauses(cleanedText);
        } catch (error) {
          // Fallback to simple text cleaning
          cleanedText = removeSpecialSectionMarkers(text);
          cleanedText = cleanTextForTTS(cleanedText);
        }
      } else if (hasSpecialSectionsDetected && settings.ttsVerbalsOnly) {
        try {
          // Extract only the verbal explanations
          const explanations = extractExplanationBlocks(text);

          if (explanations.length > 0) {
            // Join all explanations with pauses between them
            cleanedText = explanations.join('. \n\n');

            // Apply standard text cleaning to the explanations
            cleanedText = removeSpecialSectionMarkers(cleanedText);
            cleanedText = cleanTextForTTS(cleanedText);
            cleanedText = addNaturalPauses(cleanedText);
          } else {
            // If no explanations found, use the whole text but remove code sections
            cleanedText = removeSpecialSectionMarkers(text);
            cleanedText = cleanTextForTTS(cleanedText);
          }
        } catch (error) {
          // Fallback to simple text cleaning
          cleanedText = removeSpecialSectionMarkers(text);
          cleanedText = cleanTextForTTS(cleanedText);
        }
      } else if (hasSpecialSectionsDetected) {
        // If special sections are detected but verbalsOnly is not enabled,
        // read all content including explanations
        try {
          // Process the text to read regular content and explanations in the correct order
          // First, extract all blocks in order
          const explainBlockRegex = /\[\s*EXPLAIN\s*\]([\s\S]*?)\[\s*\/\s*EXPLAIN\s*\]/g;
          const codeBlockRegex = /\[\s*CODE\s*\]([\s\S]*?)\[\s*\/\s*CODE\s*\]/g;

          // Extract all blocks in order of appearance
          const allBlocks = [];
          let explainMatch;
          let codeMatch;

          // Reset regex lastIndex
          explainBlockRegex.lastIndex = 0;
          codeBlockRegex.lastIndex = 0;

          // Find all explain blocks
          while ((explainMatch = explainBlockRegex.exec(text)) !== null) {
            allBlocks.push({
              type: 'explain',
              content: explainMatch[1].trim(),
              startIndex: explainMatch.index,
              endIndex: explainMatch.index + explainMatch[0].length
            });
          }

          // Find all code blocks
          while ((codeMatch = codeBlockRegex.exec(text)) !== null) {
            allBlocks.push({
              type: 'code',
              content: codeMatch[1].trim(),
              startIndex: codeMatch.index,
              endIndex: codeMatch.index + codeMatch[0].length
            });
          }

          // Sort blocks by their appearance in the text
          allBlocks.sort((a, b) => a.startIndex - b.startIndex);

          // Extract regular content that's not in any block
          let lastEndIndex = 0;
          const regularBlocks = [];

          // Add regular content before the first block
          if (allBlocks.length > 0 && allBlocks[0].startIndex > 0) {
            regularBlocks.push({
              type: 'regular',
              content: text.substring(0, allBlocks[0].startIndex).trim(),
              startIndex: 0,
              endIndex: allBlocks[0].startIndex
            });
            lastEndIndex = allBlocks[0].endIndex;
          }

          // Add regular content between blocks
          for (let i = 0; i < allBlocks.length - 1; i++) {
            const currentBlock = allBlocks[i];
            const nextBlock = allBlocks[i + 1];

            if (nextBlock.startIndex > currentBlock.endIndex) {
              regularBlocks.push({
                type: 'regular',
                content: text.substring(currentBlock.endIndex, nextBlock.startIndex).trim(),
                startIndex: currentBlock.endIndex,
                endIndex: nextBlock.startIndex
              });
            }

            lastEndIndex = nextBlock.endIndex;
          }

          // Add regular content after the last block
          if (allBlocks.length > 0 && lastEndIndex < text.length) {
            regularBlocks.push({
              type: 'regular',
              content: text.substring(lastEndIndex).trim(),
              startIndex: lastEndIndex,
              endIndex: text.length
            });
          }

          // If no blocks were found, treat the entire text as regular content
          if (allBlocks.length === 0) {
            regularBlocks.push({
              type: 'regular',
              content: text.trim(),
              startIndex: 0,
              endIndex: text.length
            });
          }

          // Combine all blocks in order
          const combinedBlocks = [...allBlocks, ...regularBlocks].sort((a, b) => a.startIndex - b.startIndex);

          // Process blocks in order
          const processedBlocks = [];

          for (const block of combinedBlocks) {
            if (block.type === 'regular' && block.content.trim()) {
              // For regular content, include it as is
              processedBlocks.push(block.content);
            } else if (block.type === 'explain' && block.content.trim()) {
              // For explain blocks, include content without prefix
              processedBlocks.push(block.content);
            } else if (block.type === 'code' && block.content.trim()) {
              // For code blocks, don't add anything to be read
              // processedBlocks.push(""); // Empty string would add nothing
            }
          }

          // Join all blocks with pauses between them
          const shortenedText = processedBlocks.join('. ');

          // Clean the shortened text
          cleanedText = cleanTextForTTS(shortenedText);
          cleanedText = addNaturalPauses(cleanedText);

        } catch (error) {
          console.error('Error processing special sections:', error);
          // Fallback to simple text cleaning
          cleanedText = processCourseContent(text);
        }
      } else if (isCourseContent) {
        // Use specialized course content processing
        cleanedText = processCourseContent(text);
      } else {
        // Simple text cleaning for regular content
        cleanedText = removeSpecialSectionMarkers(text);
        cleanedText = cleanTextForTTS(cleanedText);
      }

      // Create a new utterance with the cleaned text
      const utterance = new SpeechSynthesisUtterance(cleanedText);

      // Always set a voice - prioritize female voices
      const voiceToUse = voiceOverride || selectedVoice;

      if (voiceToUse && voiceToUse.voiceURI) {
        utterance.voice = voiceToUse;
        // Set language to match the voice
        utterance.lang = voiceToUse.lang;
      } else {
        // If we don't have a selected voice yet, try to get one now
        const availableVoices = window.speechSynthesis.getVoices();
        if (availableVoices && availableVoices.length > 0) {
          // Try to find a female voice
          const femaleVoice = availableVoices.find(v =>
            v.name.includes('Female') ||
            v.name.includes('Zira') ||
            v.name.includes('Samantha'));

          if (femaleVoice) {
            utterance.voice = femaleVoice;
            utterance.lang = femaleVoice.lang;
          }
        }
      }

      // Set speech parameters for a natural, pleasant voice
      utterance.rate = 0.95;     // Slightly slower for better clarity
      utterance.pitch = 1.0;     // Normal pitch
      utterance.volume = settings.volume;  // Use volume from settings (0-1 range)

      // Set event handlers with proper error handling
      utterance.onstart = () => {
        setIsPlaying(true);
        setIsLoading(false);
      };

      utterance.onend = () => {
        setIsPlaying(false);
        setIsLoading(false);
      };

      utterance.onerror = (event) => {
        // Handle the error more gracefully
        setIsPlaying(false);
        setIsLoading(false);

        // After a short delay, simulate speech completion
        setTimeout(() => {
          setIsPlaying(false);
          setIsLoading(false);
        }, text.length * 50); // Rough estimate of speech duration
      };

      // Start speaking with a small delay to ensure browser is ready
      setTimeout(() => {
        try {
          window.speechSynthesis.speak(utterance);
        } catch (e) {
          console.error('Error starting speech synthesis:', e);
          // Simulate speech with a timer
          setIsPlaying(true);
          setTimeout(() => {
            setIsPlaying(false);
          }, text.length * 50);
        }
      }, 100);

      return true;
    } catch (err) {
      console.error('Error in speak function:', err);
      // Simulate speech with a timer
      setIsPlaying(true);
      setTimeout(() => {
        setIsPlaying(false);
      }, text.length * 50);

      return true; // Return true anyway so the UI shows we're handling it
    }
  }, [selectedVoice, stopSpeaking, settings, processCourseContent]);

  // Function to select a voice by name
  const selectVoiceByName = useCallback((voiceName: string) => {
    if (!voices || voices.length === 0) {
      return false;
    }

    // Try to find a voice that includes the given name (case insensitive)
    const lowerName = voiceName.toLowerCase();
    const voice = voices.find(v => v.name.toLowerCase().includes(lowerName));

    if (voice) {
      setSelectedVoice(voice);
      return true;
    } else {
      return false;
    }
  }, [voices]);

  // Function to list all available voices to the console
  const listAvailableVoices = useCallback(() => {
    if (!voices || voices.length === 0) {
      console.log('No voices available');
      return;
    }

    console.log('Available voices:');
    voices.forEach((voice, index) => {
      console.log(`${index}: ${voice.name} (${voice.lang})`);
    });
  }, [voices]);

  return {
    speak,
    stopSpeaking,
    isLoading,
    isPlaying,
    error,
    voices,
    selectedVoice,
    setSelectedVoice,
    selectVoiceByName,
    listAvailableVoices
  };
}
